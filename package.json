{"name": "portfolio-site", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "NODE_ENV=production next build", "build:dev": "NODE_ENV=production BRANCH=dev next build", "start": "next start", "export": "next export", "lint": "next lint", "lint:fix": "eslint --fix --ext ts,tsx app/ && prettier --write app/", "type-check": "tsc"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.3.0", "@fortawesome/free-brands-svg-icons": "^6.3.0", "@fortawesome/free-solid-svg-icons": "^6.3.0", "@fortawesome/react-fontawesome": "^0.2.0", "@react-buddy/ide-toolbox-next": "^2.4.1", "@react-three/drei": "^9.108.4", "@react-three/fiber": "^8.16.8", "@react-three/rapier": "^1.4.0", "@tailwindcss/line-clamp": "^0.4.2", "framer-motion": "^10.6.1", "gsap": "^3.11.5", "kinet": "^2.2.1", "leva": "^0.9.35", "lodash": "^4.17.21", "meshline": "^3.3.1", "next": "^13.4.12", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^4.10.1", "react-intersection-observer": "^9.4.3", "resize-observer-polyfill": "^1.5.1", "scroller-motion": "^1.2.3", "sharp": "^0.32.0", "three": "^0.166.1", "touch-cli": "^0.0.1", "usehooks-ts": "^2.9.1"}, "devDependencies": {"@types/node": "^20.4.5", "@types/react": "18.0.28", "@typescript-eslint/eslint-plugin": "^8.19.1", "@typescript-eslint/parser": "^8.19.1", "autoprefixer": "^10.4.13", "bun-devtools": "^0.0.2", "bun-types": "^0.7.0", "eslint-import-resolver-typescript": "^3.3.0", "eslint-plugin-import": "^2.26.0", "eslint-plugin-jsx-a11y": "^6.6.1", "eslint-plugin-unicorn": "^48.0.1", "lint-staged": "^13.0.3", "postcss": "^8.4.21", "prettier": "^2.8.4", "prettier-plugin-tailwindcss": "^0.2.5", "stylelint": "^14.9.1", "stylelint-config-prettier": "^9.0.3", "stylelint-config-recess-order": "^3.0.0", "stylelint-config-standard": "^26.0.0", "tailwind-scrollbar": "^3.0.0", "tailwindcss": "^3.2.7", "typescript": "5.1.6", "eslint": "9.16.0", "eslint-config-next": "15.1.2", "eslint-config-prettier": "^9.1.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-tailwindcss": "^3.17.5"}}