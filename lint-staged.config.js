module.exports = {
  // this will check Typescript files
  // we generate graphql TS types first
  "**/*.(ts|tsx)": () => ["npm run codegen", "npx tsc --noEmit"],

  // This will lint and format TypeScript and                                             //JavaScript files
  "**/*.(ts|tsx|js)": (filenames) => [
    `npx eslint --fix ${filenames.join(" ")}`,
    `npx prettier --write ${filenames.join(" ")}`,
  ],

  // this will Format MarkDown and JSON
  "**/*.(md|json|yml|css|md)": (filenames) =>
    `npx prettier --write ${filenames.join(" ")}`,
};
