import React from "react";
import "../animations/animate.css";
import AnimatedBody from "../animations/AnimatedBody";
import AnimatedTitle from "../animations/AnimatedTitle";
import {
  SiGit,
  SiGithub,
  SiPostgresql,
  SiMysql,
  SiJavascript,
  SiNextdotjs,
  SiReact,
  SiTypescript,
  SiMongodb,
  SiNodedotjs,
  SiSolidity,
  SiWeb3Dotjs,
  //   SiHardhat,
  //   SiFoundry,
  //   SiMetamask,
  SiEthereum,
  SiRedux,
  SiGraphql,
  SiFirebase,
  SiWebpack,
  SiJest,
  SiStorybook,
  SiDocker,
  SiJenkins,
  SiNginx,
  SiTailwindcss,
  SiMaterialdesign,
  SiFigma,
  SiFramer,
  SiChakraui,
  SiExpress,
  SiPrisma,
} from "react-icons/si";
import AnimatedTools from "../animations/AnimatedTools";

const Tools = () => {
  return (
    <section
      className="relative z-10 w-full items-center justify-center overflow-hidden bg-[#0E1016] bg-cover bg-center pb-36 pt-16 md:pb-44 md:pt-20 lg:pb-56 lg:pt-20"
      id="tools"
    >
      <div className="mx-auto flex w-[90%] flex-col items-center justify-center lg:max-w-[1212.8px]">
        <AnimatedTitle
          text={"TOOLS IM USING."}
          className={
            "mb-10 text-left text-[40px] font-bold leading-[0.9em] tracking-tighter text-[#e4ded7] sm:text-[45px] md:mb-16 md:text-[60px] lg:text-[80px]"
          }
          wordSpace={"mr-[14px]"}
          charSpace={"mr-[0.001em]"}
        />

        <div className="mx-auto w-[100%] justify-center lg:max-w-[1200px]">
          <div className="mb-10 flex w-[100%] flex-col gap-4 text-[18px] font-bold leading-relaxed tracking-wide text-[#e4ded7] md:mb-16 md:gap-6 md:text-[40px] md:leading-relaxed lg:mb-16 lg:w-[100%]">
            <AnimatedBody delay={0.1} text="Blockchain" />
            <div>
              <AnimatedTools
                className="grid grid-cols-6 gap-4"
                delay={0.1}
                stepSize={0.1}
                iconSize={50}
              >
                <SiSolidity />
                <SiWeb3Dotjs />
                {/* <SiHardhat />
                <SiFoundry />
                <SiMetamask /> */}
                <SiEthereum />
              </AnimatedTools>
            </div>
          </div>

          <div className="mb-10 flex w-[100%] flex-col gap-4 text-[18px] font-bold leading-relaxed tracking-wide text-[#e4ded7] md:mb-16 md:gap-6 md:text-[40px] md:leading-relaxed lg:mb-16 lg:w-[100%]">
            <AnimatedBody delay={0.2} text="Frontend" />
            <div>
              <AnimatedTools
                className="grid grid-cols-6 gap-4"
                delay={0.2}
                stepSize={0.1}
                iconSize={50}
              >
                <SiReact />
                <SiNextdotjs />
                <SiTypescript />
                <SiJavascript />
                <SiRedux />
                <SiGraphql />
              </AnimatedTools>
            </div>
          </div>

          <div className="mb-10 flex w-[100%] flex-col gap-4 text-[18px] font-bold leading-relaxed tracking-wide text-[#e4ded7] md:mb-16 md:gap-6 md:text-[40px] md:leading-relaxed lg:mb-16 lg:w-[100%]">
            <AnimatedBody delay={0.3} text="Backend & Database" />
            <div>
              <AnimatedTools
                className="grid grid-cols-6 gap-4"
                delay={0.3}
                stepSize={0.1}
                iconSize={50}
              >
                <SiNodedotjs />
                <SiExpress />
                <SiPrisma />
                <SiPostgresql />
                <SiMongodb />
                <SiFirebase />
              </AnimatedTools>
            </div>
          </div>

          <div className="mb-10 flex w-[100%] flex-col gap-4 text-[18px] font-bold leading-relaxed tracking-wide text-[#e4ded7] md:mb-16 md:gap-6 md:text-[40px] md:leading-relaxed lg:mb-16 lg:w-[100%]">
            <AnimatedBody delay={0.4} text="Testing & DevOps" />
            <div>
              <AnimatedTools
                className="grid grid-cols-6 gap-4"
                delay={0.4}
                stepSize={0.1}
                iconSize={50}
              >
                <SiJest />
                <SiStorybook />
                <SiDocker />
                <SiJenkins />
                <SiGit />
                <SiNginx />
              </AnimatedTools>
            </div>
          </div>

          <div className="mb-10 flex w-[100%] flex-col gap-4 text-[18px] font-bold leading-relaxed tracking-wide text-[#e4ded7] md:mb-16 md:gap-6 md:text-[40px] md:leading-relaxed lg:mb-16 lg:w-[100%]">
            <AnimatedBody delay={0.5} text="Design & UI" />
            <div>
              <AnimatedTools
                className="grid grid-cols-6 gap-4"
                delay={0.5}
                stepSize={0.1}
                iconSize={50}
              >
                <SiTailwindcss />
                <SiMaterialdesign />
                <SiFigma />
                <SiFramer />
                <SiChakraui />
              </AnimatedTools>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Tools;
