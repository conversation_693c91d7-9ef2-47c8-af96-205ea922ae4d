import { inter } from "../fonts/inter";
import { motion } from "framer-motion";
import HeroBackground from "../components/background/HeroBackground";
import React from "react";
import AnimatedTitle from "../animations/AnimatedTitle";
import Image from "next/image";

const Hero = () => {
  return (
    <motion.section
      className="relative z-10 flex h-[100vh] w-full justify-center"
      id="home"
      initial="initial"
      animate="animate"
    >
      <HeroBackground />
      <div className="mt-10 flex flex-col items-center justify-center sm:mt-0">
        <div
          className={`relative flex flex-col items-center justify-center ${inter.className} pointer-events-none`}
        >
          <div className="flex items-start gap-4">
            <AnimatedTitle
              text={"Hi, I'm <PERSON>"}
              className={
                "mb-1 text-left text-[40px] font-bold leading-[0.9em] tracking-tighter text-[#e4ded7] sm:text-[45px] md:mb-16 md:text-[60px] lg:text-[80px]"
              }
              wordSpace={"mr-[10px]"}
              charSpace={"mr-[0.001em]"}
            />
            <Image
              src="https://pbs.twimg.com/profile_images/1837114580641710080/zztwyLlV_400x400.jpg"
              alt="Mike R."
              width={80}
              height={80}
              className="pointer-events-auto rounded-full"
            />
          </div>
        </div>
      </div>
    </motion.section>
  );
};

export default Hero;
