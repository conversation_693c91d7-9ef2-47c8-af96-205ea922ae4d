import localFont from "next/font/local";

export const inter = localFont({
    src: [
        {
            path: "./Inter-Bold.ttf",
            weight: "900",
            style: "normal",
        },
        {
            path: "./Inter-Bold.ttf",
            weight: "900",
            style: "italic",
        },
        {
            path: "./Inter-Bold.ttf",
            weight: "700",
            style: "normal",
        },
        {
            path: "./Inter-Bold.ttf",
            weight: "700",
            style: "italic",
        },
        {
            path: "./Inter-Light.ttf",
            weight: "300",
            style: "normal",
        },
        {
            path: "./Inter-Light.ttf",
            weight: "300",
            style: "italic",
        },
        {
            path: "./Inter-Medium.ttf",
            weight: "500",
            style: "normal",
        },
        {
            path: "./Inter-Medium.ttf",
            weight: "500",
            style: "italic",
        },
        {
            path: "./Inter-Regular.ttf",
            weight: "600",
            style: "normal",
        },
        {
            path: "./Inter-Regular.ttf",
            weight: "600",
            style: "italic",
        },
        {
            path: "./Inter-Bold.ttf",
            weight: "800",
            style: "normal",
        },
        {
            path: "./Inter-Light.ttf",
            weight: "200",
            style: "normal",
        },
        {
            path: "./Inter-Light.ttf",
            weight: "400",
            style: "italic",
        },
    ],
});
