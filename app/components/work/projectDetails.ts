import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>extdotjs,
  SiReact,
  SiTailwindcss,
  SiTypescript,
  SiGraphql,
  SiPostgresql,
  SiShopify,
  //   SiSanity,
  SiD3Dotjs,
  //   SiWebsocket,
  SiRedis,
  //   SiNodeDotJs,
  SiExpress,
  SiFirebase,
  SiStorybook,
  SiJavascript,
  SiWeb3Dotjs,
  SiEthereum,
} from "react-icons/si";
import { FaNodeJs, FaTelegram } from "react-icons/fa";
import { IconType } from "react-icons";

export type ProjectProps = {
  id: number;
  name: string;
  description: string;
  technologies: IconType[];
  techNames: string[];
  techLinks: string[];
  github?: string;
  demo: string;
  image: string;
  available: boolean;
};

export const projects = [
  {
    id: 0,
    name: "Focus App",
    description:
      "The crypto social network. Now live on TestNet. Focus is now live in an invite-only testnet phase! Launch App on Testnet..",
    technologies: [
      SiTypescript,
      SiReact,
      SiNextdotj<PERSON>,
      <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
      <PERSON><PERSON><PERSON><PERSON>,
      Si<PERSON>ostgresql,
      SiGraphql,
    ],
    techNames: [
      "TypeScript",
      "React",
      "Next.js",
      "Tailwind CSS",
      "Framer Motion",
      "PostgreSQL",
      "GraphQL",
    ],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://tailwindcss.com/",
      "https://www.framer.com/motion/",
      "https://www.postgresql.org/",
      "https://graphql.org/",
    ],
    demo: "https://www.focus.xyz/",
    image: "/projects/focus.png",
    available: true,
  },
  {
    id: 13,
    name: "Zero Trust",
    description:
      "Interactive game simulating Mac environment and social phishing attacks. Features complex frontend with advanced UI components and realistic system interactions to educate users about cybersecurity threats.",
    technologies: [SiReact, SiTailwindcss, SiJavascript, SiTypescript],
    techNames: ["React", "Tailwind CSS", "JavaScript", "TypeScript"],
    techLinks: [
      "https://reactjs.org/",
      "https://tailwindcss.com/",
      "https://developer.mozilla.org/en-US/docs/Web/JavaScript",
      "https://www.typescriptlang.org/",
    ],
    demo: "https://www.zero-trust-game.xyz/",
    image: "/projects/zerotrust.png",
    available: true,
  },
  {
    id: 14,
    name: "Bera Borrower",
    description:
      "DeFi staking application on Bera chain with advanced vault management. Implemented real-time updates, complex state management, and seamless asset staking functionality using cutting-edge Web3 technologies.",
    technologies: [SiNextdotjs, SiReact, SiTypescript, SiWeb3Dotjs, SiEthereum],
    techNames: ["Next.js", "React", "TypeScript", "Web3.js", "Ethereum"],
    techLinks: [
      "https://nextjs.org/",
      "https://reactjs.org/",
      "https://www.typescriptlang.org/",
      "https://web3js.readthedocs.io/",
      "https://ethereum.org/",
    ],
    demo: "https://www.beraborrow.com/",
    image: "/projects/beraborrow.png",
    available: true,
  },
  {
    id: 15,
    name: "Prem Mini App",
    description:
      "Telegram mini app built on serverless architecture with Firebase. Handles complex full-stack logic for Telegram gifts marketplace with TON blockchain integration, featuring scalable cloud functions.",
    technologies: [SiFirebase, FaTelegram, SiJavascript, SiReact],
    techNames: ["Firebase", "Telegram", "JavaScript", "React"],
    techLinks: [
      "https://firebase.google.com/",
      "https://telegram.org/",
      "https://developer.mozilla.org/en-US/docs/Web/JavaScript",
      "https://reactjs.org/",
    ],
    demo: "https://t.me/premarketgiftbot",
    image: "/projects/prem.png",
    available: true,
  },
  {
    id: 16,
    name: "Safe Check",
    description:
      "Mini app providing comprehensive logout instructions for popular applications. Built with clean, responsive design using modern React stack and Tailwind CSS for optimal user experience.",
    technologies: [SiNextdotjs, SiReact, SiTailwindcss, SiTypescript],
    techNames: ["Next.js", "React", "Tailwind CSS", "TypeScript"],
    techLinks: [
      "https://nextjs.org/",
      "https://reactjs.org/",
      "https://tailwindcss.com/",
      "https://www.typescriptlang.org/",
    ],
    demo: "https://safe-check.vercel.app/",
    image: "/projects/safe-check.png",
    available: true,
  },
  {
    id: 12,
    name: "SmartSecHub",
    description:
      "The biggest in the world web3 security index where you can find more than 5k links for articles, white-papers, videos, tweets, GitHub repos related to web3 security. There are more than 100 categories which you can filter. This is a web3 security database of collective wisdom.",
    technologies: [SiReact, SiNextdotjs, SiTailwindcss, SiFirebase],
    techNames: ["React", "Next.js", "Tailwind CSS", "Firebase"],
    techLinks: [
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://tailwindcss.com/",
      "https://firebase.google.com/",
    ],
    demo: "https://spectacular-dieffenbachia-f97c5f.netlify.app/",
    image: "/projects/smartsechub.png",
    available: true,
  },
  {
    id: 10,
    name: "Daily Warden",
    description:
      "Site for web3 security auditors where users can find active security contexts and participate in it. Co-developed with https://x.com/GalloDaSballo.",
    technologies: [SiReact, SiNextdotjs, SiTailwindcss],
    techNames: ["React", "Next.js", "Tailwind CSS"],
    techLinks: [
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://tailwindcss.com/",
    ],
    demo: "https://www.dailywarden.com/",
    image: "/projects/dailywarden.png",
    available: true,
  },
  {
    id: 11,
    name: "Recon.xyz",
    description:
      "Run invariant testing with Echidna, Medusa and Foundry. SaaS solution from solidity fuzzing engineers. Involved in early stage to work on UI stuff.",
    technologies: [SiGithub, SiNextdotjs, SiReact, SiTailwindcss],
    techNames: ["GitHub", "Next.js", "React", "Tailwind CSS", "Sanity"],
    techLinks: [
      "https://github.com/",
      "https://nextjs.org/",
      "https://reactjs.org/",
      "https://tailwindcss.com/",
      "https://www.sanity.io/",
    ],
    demo: "https://getrecon.xyz/",
    image: "/projects/recon.png",
    available: true,
  },
  {
    id: 6,
    name: "Nomad Homes",
    description:
      "Emirates real-estate platform with custom property filters, map-based search, and premium features for buying and selling properties.",
    technologies: [SiTypescript, SiReact, SiNextdotjs, SiExpress, SiPostgresql],
    techNames: ["TypeScript", "React", "Next.js", "Express", "PostgreSQL"],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://expressjs.com/",
      "https://www.postgresql.org/",
    ],
    demo: "https://nomadhomes.ae/",
    image: "/projects/nomad.png",
    available: true,
  },
  {
    id: 3,
    name: "Sandsiv+",
    description:
      "B2B solution for customer feedback analysis featuring advanced D3.js data visualization with 2D and 3D charts. Implemented WebSocket notification service and MongoDB filter parser.",
    technologies: [
      SiTypescript,
      SiReact,
      SiNextdotjs,
      SiD3Dotjs,
      //   SiWebsocket,
      SiRedis,
      FaNodeJs,
    ],
    techNames: [
      "TypeScript",
      "React",
      "Next.js",
      "D3.js",
      "WebSocket",
      "Redis",
      "Node.js",
    ],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://d3js.org/",
      //   "https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API",
      "https://redis.io/",
      "https://nodejs.org/",
    ],
    demo: "https://sandsiv.com/advanced-dashboard/",
    image: "/projects/sandsiv.png",
    available: true,
  },
  {
    id: 4,
    name: "iReceipt",
    description:
      "Cross-platform React and Ionic application for receipt management, featuring offline functionality and data synchronization across devices.",
    technologies: [SiTypescript, SiReact],
    techNames: ["TypeScript", "React"],
    techLinks: ["https://www.typescriptlang.org/", "https://reactjs.org/"],
    demo: "https://ireceipt.io/",
    image: "/projects/ireceipt.png",
    available: true,
  },
  {
    id: 7,
    name: "Catchco.com",
    description:
      "High-performance eCommerce platform with GraphQL, Shopify, and Algolia integration, serving 100k monthly users with custom design system.",
    technologies: [
      SiTypescript,
      SiReact,
      SiNextdotjs,
      SiShopify,
      SiTailwindcss,
      //   SiSanity,
    ],
    techNames: [
      "TypeScript",
      "React",
      "Next.js",
      "Shopify",
      "Tailwind CSS",
      "Sanity",
    ],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://www.shopify.com/",
      "https://tailwindcss.com/",
      //   "https://www.sanity.io/",
    ],
    demo: "https://catchco.com/",
    image: "/projects/catchoo.png",
    available: true,
  },
  {
    id: 1,
    name: "IVE.ONE",
    description:
      "Digital asset infrastructure for financial institutions, enabling blockchain banking integration and digital asset trading capabilities through direct infrastructure integration.",
    technologies: [SiTypescript, SiReact, SiNextdotjs, SiD3Dotjs],
    techNames: ["TypeScript", "React", "Next.js", "D3.js"],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://d3js.org/",
    ],
    demo: "https://www.linkedin.com/company/iveone/?originalSubdomain=de",
    image: "",
    available: true,
  },
  {
    id: 8,
    name: "X-Bees",
    description:
      "Customer communication management solution with WebRTC integration for P2P communication, available as both mobile app and PWA.",
    technologies: [SiTypescript, SiReact],
    techNames: ["TypeScript", "React", "WebSocket"],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      //   "https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API",
    ],
    demo: "https://www.wildix.com/x-bees/",
    image: "/projects/x-bees.png",
    available: true,
  },
  {
    id: 2,
    name: "Certa Scale",
    description:
      "AWS-like cloud management platform for Kubernetes clusters, featuring complex data forms and visualization of large datasets. Implemented advanced caching with IndexedDB for 2MB+ API responses.",
    technologies: [
      SiTypescript,
      SiReact,
      SiNextdotjs,
      SiD3Dotjs,
      //   SiWebsocket,
      FaNodeJs,
    ],
    techNames: [
      "TypeScript",
      "React",
      "Next.js",
      "D3.js",
      "WebSocket",
      "Node.js",
    ],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://nextjs.org/",
      "https://d3js.org/",
      //   "https://developer.mozilla.org/en-US/docs/Web/API/WebSockets_API",
      "https://nodejs.org/",
    ],
    demo: "https://certascale.io/index.html",
    image: "/projects/certa.png",
    available: true,
  },
  {
    id: 9,
    name: "Electronic Records",
    description:
      "Secure medical application for early cancer detection, featuring real-time form creation and Azure integration.",
    technologies: [SiTypescript, SiReact, SiStorybook],
    techNames: ["TypeScript", "React", "Storybook"],
    techLinks: [
      "https://www.typescriptlang.org/",
      "https://reactjs.org/",
      "https://storybook.js.org/",
    ],
    demo: "https://www.foundationmedicine.com/",
    image: "/projects/ebr.png",
    available: true,
  },
];
