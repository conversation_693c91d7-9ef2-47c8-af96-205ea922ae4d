import "./globals.css";
import React, { ReactNode } from "react";
import type { Metada<PERSON> } from "next";
import { Inter } from "next/font/google";

const syne = Inter({
  subsets: ["latin"],
  display: "block",
  weight: ["400", "500", "600", "700", "800"],
});

export const metadata: Metadata = {
  metadataBase: new URL("https://mike-projects.netlify.app/"),
  title: "Portfolio - Mike R.",
  description: "Personal project of awesome full-stack web developer <PERSON>",
  generator: "Next.js",
  applicationName: "Portfolio - Mike R.",
  keywords: [
    "Portfolio",
    "React",
    "developer",
    "frontend",
    "nextjs",
    "react",
    "frontend developer",
    "frontend engineer",
    "creative",
    "creative developer",
    "creative engineer",
    "tech",
    "software",
    "software developer",
    "portfolio",
    "frontend developer portfolio",
    "creative developer portfolio",
    "creative engineer portfolio",
    "software developer portfolio",
    "frontend engineer portfolio",
  ],
  colorScheme: "dark",
  openGraph: {
    title: "Portfolio Mike R<PERSON>",
    description: "Personal project of awesome full-stack web developer <PERSON>",
    url: "https://mike-projects.netlify.app/",
    siteName: "https://mike-projects.netlify.app/",
    images: [
      {
        url: "./public/metadata.jpg",
        width: 1200,
        height: 630,
        alt: "Portfolio - Mike R.",
      },
      {
        url: "https://pbs.twimg.com/profile_images/1837114580641710080/zztwyLlV_400x400.jpg",
        width: 400,
        height: 400,
        alt: "Mike R. Profile Image",
      },
    ],
    locale: "en-US",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Portfolio Mike R.",
    description: "Personal project of awesome full-stack web developer Mike R.",
    creator: "@0xmrudenko",
    creatorId: "0000000000",
    images: [
      "./public/metadata.jpg",
      "https://pbs.twimg.com/profile_images/1837114580641710080/zztwyLlV_400x400.jpg",
    ],
  },
  robots: {
    index: true,
    follow: true,
    nocache: false,
    googleBot: {
      index: true,
      follow: false,
      noimageindex: true,
      "max-video-preview": -1,
      "max-image-preview": "large",
      "max-snippet": -1,
    },
  },
  category: "technology",
};

type RootLayoutProps = {
  children: ReactNode;
};

export default function RootLayout({ children }: RootLayoutProps) {
  return (
    <html lang="en">
      <body
        className={`${syne.className} scroll-smooth scrollbar-none scrollbar-track-[#0E1016] scrollbar-thumb-[#212531]`}
      >
        {children}
      </body>
    </html>
  );
}
