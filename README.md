<div align="center">
  <a href="https://github.com/karthikmudunuri/eldoraui">


 <img src="https://github.com/user-attachments/assets/d00f15e8-cc1a-47a6-b99f-885156a55fc3" alt="Logo" width="60" height="60">
  </a>
  <h1 align="center">Eldora UI</h1>
  <p align="center">
   open-source animated components built with React, Typescript, Tailwind CSS, and Framer Motion.
100% open-source, and customizable.
  </p>
  <p>
    
   <a href="https://www.eldoraui.site/">Visit site</a>
    ·
    <a href="https://github.com/karthikmudunuri/eldoraui/issues">Report Bug</a>
    ·
    <a href="https://github.com/karthikmudunuri/eldoraui/issues">Request Feature</a>
  </p>
</div>

<!-- ABOUT THE TEMPLATE -->

<div align="center">

 <img width="1425" alt="Portfolio-template" src="https://github.com/user-attachments/assets/265fa13e-98ed-4c99-bd7f-ce7efe19627f">

 
</div>

# Portfolio 

Minimalist developer portfolio using Next.js 14, React, TailwindCSS, Framer motion

# Features

- Built using Next.js 14, React, Typescript, Shadcn/UI, TailwindCSS, Framer Motion, Eldora UI
- Responsive for different devices
- Optimized for Next.js and Vercel

# Getting Started Locally

1. Clone this repository to your local machine:

   ```bash
   git clone https://github.com/karthikmudunuri/portfolio-template
   ```

2. Move to the cloned directory

   ```bash
   cd portfolio-template
   ```

3. Install dependencies:

   ```bash
   npm install
   ```

4. Start the local Server:

   ```bash
   npm run dev
   ```

5. Open the [Sections](https://github.com/karthikmudunuri/portfolio-template/tree/main/app/sections) folder and make changes

# License

Licensed under the [MIT license](https://github.com/karthikmudunuri/portfolio-template/blob/main/LICENSE.md).
